import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: true, // Listen on all addresses
    port: 5173,
    strictPort: true, // Ensure Vite uses this port or fails if it's in use
    open: true, // Open browser automatically
    hmr: {
      // Explicitly configure HMR WebSocket connection
      protocol: 'ws',
      host: 'localhost',
      port: 5173
    },
    // Disable service worker in development
    middlewareMode: false,
    fs: {
      // Allow serving files from one level up to the project root
      allow: ['..']
    }
  },
  plugins: [
    react(),
    // mode === 'development' && componentTagger(), // Temporarily commented out
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  // Performance-optimized build configuration for 95+ speed score
  build: {
    // Generate source maps only for development
    sourcemap: mode === 'development',
    // Advanced CSS optimization
    cssCodeSplit: false, // Single CSS file for better performance
    cssMinify: 'lightningcss',
    // Optimize for production with aggressive settings
    minify: mode === 'production' ? 'esbuild' : false,
    // Target modern browsers for better optimization
    target: 'es2020',
    // Optimize chunk sizes for better loading
    chunkSizeWarningLimit: 500,
    // Advanced Rollup options for performance
    rollupOptions: {
      onwarn(warning, warn) {
        // Skip certain warnings in development
        if (mode === 'development' && warning.code === 'UNUSED_EXTERNAL_IMPORT') {
          return;
        }
        warn(warning);
      },
      output: {
        // Aggressive chunk optimization for speed
        manualChunks: (id) => {
          // Create larger, more efficient chunks
          if (id.includes('node_modules')) {
            if (id.includes('react') || id.includes('react-dom')) {
              return 'vendor-react';
            }
            if (id.includes('@radix-ui') || id.includes('lucide-react')) {
              return 'vendor-ui';
            }
            if (id.includes('framer-motion')) {
              return 'vendor-motion';
            }
            return 'vendor-libs';
          }
          // Group internal components
          if (id.includes('/src/components/')) {
            return 'components';
          }
          if (id.includes('/src/pages/')) {
            return 'pages';
          }
        },
        // Optimize asset naming for better caching
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.');
          const ext = info[info.length - 1];
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
            return `assets/img/[name]-[hash:8][extname]`;
          }
          if (/css/i.test(ext)) {
            return `assets/css/[name]-[hash:8][extname]`;
          }
          return `assets/[name]-[hash:8][extname]`;
        },
        chunkFileNames: 'assets/js/[name]-[hash:8].js',
        entryFileNames: 'assets/js/[name]-[hash:8].js'
      }
    },
    // Enable advanced optimizations
    reportCompressedSize: false, // Disable for faster builds
    // Optimize dependencies
    commonjsOptions: {
      include: [/node_modules/],
      transformMixedEsModules: true
    },
    // Additional performance optimizations
    assetsInlineLimit: 4096, // Inline small assets
    emptyOutDir: true
  },
  // Optimize preview server
  preview: {
    port: 8080,
    // Enable HTTP compression for better performance
    compress: true,
  },
  // Performance-optimized dependency handling
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'framer-motion',
      '@radix-ui/react-dialog',
      '@radix-ui/react-dropdown-menu',
      '@radix-ui/react-navigation-menu',
      'clsx',
      'tailwind-merge'
    ],
    exclude: ['@vercel/analytics', '@vercel/speed-insights'],
    // Force optimization for better performance
    force: mode === 'production'
  },
  // CSS optimization
  css: {
    devSourcemap: mode === 'development',
    lightningcss: {
      minify: mode === 'production',
      targets: {
        chrome: 90,
        firefox: 88,
        safari: 14,
        edge: 90
      }
    }
  },
  // Advanced performance optimizations
  esbuild: {
    // Remove console logs in production
    drop: mode === 'production' ? ['console', 'debugger'] : [],
    // Optimize for modern browsers
    target: 'es2020',
    // Enable tree shaking
    treeShaking: true
  },
  // Worker optimization
  worker: {
    format: 'es'
  }
}));
